<template>
  <!-- 开屏广告 -->
  <view class="splash-ad" v-if="showSplashAd && currentSplashAd">
    <image class="splash-image" :src="currentSplashAd.imageUrl" mode="aspectFill" @click="handleAdClick" />
    <view class="skip-container" @click="skipAd">
      <text class="skip-text">{{ adCountdown }}s 跳过</text>
    </view>
    <view class="ad-label">广告</view>
  </view>

  <view class="container">
    <!-- 顶部标签导航 -->
    <view class="top-tabs">
      <!-- Logo 部分 -->
      <view class="logo-area">
        <view @click="goToRecommend" class="logo-link">
          <image class="app-logo" src="/static/logo.png" mode="aspectFit"/>
        </view>
      </view>

      <view class="right-icons">

        <ul class="right-menu">
          <li @click="navigateTo('/pages/search/search')">
            <i class="icon sousuo"></i>搜索
          </li>
          <li @click="navigateTo('/pages/history/history')">
            历史
          </li>
          <li @click="navigateTo('/pages/navigation/navigation')">
            全部频道
          </li>
          <li @click="savePage">
            保存网址
          </li>
        </ul>
      </view>




    </view>


    <view class="savepage" v-show="showSavePage">
      <view class="close" @click="hideSavePage"><svg t="1748242082575" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2618"><path d="M576 512l277.333333 277.333333-64 64-277.333333-277.333333L234.666667 853.333333 170.666667 789.333333l277.333333-277.333333L170.666667 234.666667 234.666667 170.666667l277.333333 277.333333L789.333333 170.666667 853.333333 234.666667 576 512z" fill="#ffffff"></path></svg></view>
      <h3>最新公告</h3>
      <view class="savecon">
        <view>
          <ul>
            <li>站最新域名:</li>
            <li>{{ systemSetting.domain }}</li>
            <li>发送任意邮件至邮箱</li>
            <li>{{ systemSetting.contactEmail }}</li>
            <li>秒得最新地址</li>
          </ul>
        </view>
        <view>
          <view v-if="loadingQRCode" class="qr-loading">
            正在生成二维码...
          </view>
          <img v-else-if="qrCodeBase64" :src="qrCodeBase64" alt="" download="天空影视二维码.png" @click="downloadQRCode">
          <img v-else src="data:image/png;base64,+JJggg==" alt="" download="天空影视二维码.png">
          点击保存二维码
        </view>
      </view>
      <view class="jieping">
        请截屏保存，以防走失!
      </view>
    </view>


    <!-- 内容区域 -->
    <view class="content-area">

      <MenuList />

      <!-- 首页顶部通栏广告，从上而下排列 -->
      <view class="top-banners" v-if="topBanners.length > 0">
        <view
          v-for="(banner, index) in topBanners"
          :key="index"
          class="top-banner"
          @click="handleBannerClick(banner)"
        >
          <image :src="banner.imageUrl" mode="widthFix" class="banner-image" />
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading && !tabs.length" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载导航数据...</text>
      </view>

      <!-- 错误提示 -->
      <view v-else-if="error && !tabs.length" class="error-container">
        <text class="error-text">{{ error }}</text>
        <view class="error-retry" @click="loadNavigations">点击重试</view>
      </view>

      <!-- 标签列表为空时显示加载中 -->
      <view v-else-if="!tabs.length" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载导航数据...</text>
      </view>



      <!-- 使用动态组件显示不同标签的内容 -->
      <template v-else>
        <keep-alive include="RecommendContent,MovieContent,ShortVideoContent">
          <component
            :is="currentComponent"
            :key="currentTabIndex"
            :videoType="currentTabIndex > 0 ? tabs[currentTabIndex].categotyName : null"
            :videoTypeId="currentTabIndex > 0 ? tabs[currentTabIndex].categotyId : null"
          ></component>
        </keep-alive>
      </template>
    </view>
  </view>
</template>

<script>
import RecommendContent from '@/components/index/RecommendContent.vue'
import MovieContent from '@/components/index/MovieContent.vue'
import ShortVideoContent from '@/components/index/ShortVideoContent.vue'
import MenuList from "@/components/common/menu.vue"

// 导入API服务
import { filterMobileNavigations, convertToTabs } from '@/api/navigation'
import { getCachedNavigationList } from '@/api/navigationCache'
import { getAdvertisements, filterAdvertisementsByPosition, AdvertisementPosition } from '@/api/advertisement'
import { getCachedSystemSetting } from '@/api/systemSetting'
import { generateQRCodeForUniApp, formatUrl } from '@/utils/qrcode'
import logger from '@/utils/logger'

export default {
  // 注册组件
  props: {
    // 接收tab参数，避免Vue警告
    tab: {
      type: [String, Number],
      default: null
    }
  },
  components: {
    RecommendContent,
    MovieContent,
    ShortVideoContent,
    MenuList
  },
  data() {
    return {
      currentTab: 'recommend', // 默认显示推荐页面
      currentTabIndex: 0, // 当前选中的标签索引
      showOptionsBox: false, // 控制选项菜单显示状态
      loading: false, // 加载状态
      error: null, // 错误信息
      tabs: [], // 导航标签数组
      originalNavigations: [], // 原始导航数据
      topBanners: [], // 首页顶部广告

      // 开屏广告相关数据
      showSplashAd: true, // 是否显示开屏广告
      adCountdown: 10, // 广告倒计时
      adTimer: null, // 广告定时器
      splashAds: [], // 开屏广告数据
      currentAdIndex: 0, // 当前显示的广告索引
      currentSplashAd: null, // 当前显示的广告数据
      showSavePage: false, // 控制保存页面弹出框显示

      // 系统设置相关数据
      systemSetting: {
        domain: 'aaa.com',
        contactEmail: '<EMAIL>'
      },
      qrCodeBase64: '', // 二维码base64数据
      loadingQRCode: false // 二维码生成状态
    };
  },
  computed: {
    // 根据当前选中的标签返回对应的组件
    currentComponent() {
      // 如果标签数组为空，返回推荐组件
      if (!this.tabs || this.tabs.length === 0) {
        return RecommendContent;
      }

      // 如果当前标签索引超出范围，返回推荐组件
      if (this.currentTabIndex < 0 || this.currentTabIndex >= this.tabs.length) {
        return RecommendContent;
      }

      // 获取当前标签
      const currentTab = this.tabs[this.currentTabIndex];

      // 如果是推荐标签，返回推荐组件
      if (currentTab.name === '推荐') {
        return RecommendContent;
      }

      // 解析extraParams字段，现在它是一个JSON字符串
      let extraParamsObj = null;
      try {
        if (currentTab.extraParams && typeof currentTab.extraParams === 'string') {
          extraParamsObj = JSON.parse(currentTab.extraParams);
        }
      } catch (error) {
        console.error('解析extraParams失败:', error, currentTab.extraParams);
      }

      // 根据解析后的对象判断使用哪个组件
      // 如果有isHorizontalLayout属性且为true，使用ShortVideoContent
      if (extraParamsObj && extraParamsObj.isHorizontalLayout === true) {
        return ShortVideoContent;
      } else {
        return MovieContent;
      }
    },

    // 调试用计算属性：检查广告显示状态
    adDebugInfo() {
      return {
        showSplashAd: this.showSplashAd,
        splashAdsCount: this.splashAds.length,
        topBannersCount: this.topBanners.length,
        shouldShowTopBanners: this.topBanners.length > 0,
        currentSplashAd: this.currentSplashAd
      };
    }
  },
  watch: {
    // 监控顶部广告数据变化
    topBanners: {
      handler(newVal, oldVal) {
        logger.log('[广告监控] 顶部广告数据变化:', {
          oldCount: oldVal ? oldVal.length : 0,
          newCount: newVal ? newVal.length : 0,
          newData: newVal
        });
      },
      deep: true,
      immediate: true
    },
    // 监控开屏广告显示状态
    showSplashAd: {
      handler(newVal, oldVal) {
        logger.log('[广告监控] 开屏广告显示状态变化:', {
          from: oldVal,
          to: newVal,
          topBannersCount: this.topBanners.length
        });
      },
      immediate: true
    }
  },
  async onLoad(options) {
    logger.log('[开屏广告] 首页加载，准备显示开屏广告');

    // 先加载开屏广告数据
    await this.loadSplashAds();

    // 如果有开屏广告数据，开始倒计时
    if (this.splashAds && this.splashAds.length > 0) {
      this.startAdCountdown();
    } else {
      // 如果没有开屏广告，直接隐藏开屏广告
      this.showSplashAd = false;
    }

    // 并行加载导航数据、页面内广告和系统设置
    await Promise.all([
      this.loadNavigations(),
      this.loadAdvertisements(), // 确保页面内广告也被加载
      this.loadSystemSetting() // 加载系统设置
    ]);

    // 恢复上次选择的标签页（优先使用URL参数，然后使用本地存储）
    this.restoreTabState();

    // 调试信息：检查广告加载状态
    logger.log('[页面加载] 广告加载完成，开屏广告数量:', this.splashAds.length);
    logger.log('[页面加载] 页面内广告数量:', this.topBanners.length);
    logger.log('[页面加载] 开屏广告显示状态:', this.showSplashAd);

    // 如果有指定的标签参数，切换到对应的标签
    if (options) {
      if (options.tab !== undefined) {
        const tabParam = options.tab;

        // 尝试解析为数字（索引）
        const tabIndex = parseInt(tabParam);
        if (!isNaN(tabIndex) && tabIndex >= 0 && tabIndex < this.tabs.length) {
          this.switchTab(tabIndex);
        }
        // 如果不是数字，可能是categotyId
        else if (this.tabs && this.tabs.length > 0) {
          const index = this.tabs.findIndex(tab => tab.categotyId === tabParam);
          if (index !== -1) {
            this.switchTab(index);
          }
        }
      } else if (options.category !== undefined) {
        const category = decodeURIComponent(options.category);
        this.handleSwitchTabByCategory(category);
      }
    }

    // 页面加载后，确保选中的标签可见
    this.$nextTick(() => {
      this.ensureTabVisible();
    });

    // 监听切换标签事件
    uni.$on('switch-tab', this.handleSwitchTab);

    // 监听根据类别名称切换标签事件
    uni.$on('switch-tab-by-category', this.handleSwitchTabByCategory);

    // 监听获取标签数据事件
    uni.$on('get-tabs-data', this.handleGetTabsData);
  },

  onUnload() {
    // 移除事件监听
    uni.$off('switch-tab', this.handleSwitchTab);
    uni.$off('switch-tab-by-category', this.handleSwitchTabByCategory);
    uni.$off('get-tabs-data', this.handleGetTabsData);
  },

  // 页面触底时触发加载更多
  onReachBottom() {
    uni.$emit('page-reach-bottom');
  },

  // 下拉刷新
  onPullDownRefresh() {
    uni.$emit('page-pull-down-refresh');
  },
  methods: {

    // 切换选项菜单显示状态
    toggleOptionsBox() {
      this.showOptionsBox = !this.showOptionsBox;
    },
    // 隐藏选项菜单
    hideOptionsBox() {
      this.showOptionsBox = false;
      this.showSavePage = false
    },

    async savePage() {
      this.showSavePage = true; // 显示弹出框
      // 重新加载系统设置以获取最新数据
      await this.loadSystemSetting();
    },
    hideSavePage() {
      this.showSavePage = false; // 隐藏弹出框
      this.showOptionsBox = false;
    },

    /**
     * 加载系统设置
     */
    async loadSystemSetting() {
      try {
        logger.log('[系统设置] 开始加载系统设置');

        // 获取系统设置数据
        const setting = await getCachedSystemSetting();

        if (setting) {
          // 更新系统设置数据
          this.systemSetting = {
            domain: setting.domain || 'aaa.com',
            contactEmail: setting.contactEmail || '<EMAIL>'
          };

          logger.log('[系统设置] 系统设置加载成功:', this.systemSetting);

          // 生成二维码
          await this.generateQRCode();
        } else {
          logger.warn('[系统设置] 未获取到系统设置数据，使用默认值');
        }
      } catch (error) {
        logger.error('[系统设置] 加载系统设置失败:', error);
        // 使用默认值
        this.systemSetting = {
          domain: 'aaa.com',
          contactEmail: '<EMAIL>'
        };
      }
    },

    /**
     * 生成二维码
     */
    async generateQRCode() {
      try {
        this.loadingQRCode = true;

        const originalUrl = this.systemSetting.domain;
        const formattedUrl = formatUrl(originalUrl);

        logger.log('[二维码] 原始网址:', originalUrl);
        logger.log('[二维码] 格式化后:', formattedUrl);

        // 生成网址的二维码（使用格式化后的URL）
        const qrCodeData = await generateQRCodeForUniApp(originalUrl, {
          size: 200,
          backgroundColor: '#FFFFFF',
          foregroundColor: '#000000',
          autoFormat: true // 启用自动格式URL
        });

        this.qrCodeBase64 = qrCodeData;
        logger.log('[二维码] 二维码生成成功');

        // 显示成功提示
        if (originalUrl !== formattedUrl) {
          logger.log(`[二维码] URL已自动格式化: ${originalUrl} -> ${formattedUrl}`);
        }

      } catch (error) {
        logger.error('[二维码] 生成二维码失败:', error);
        // 使用默认的空白二维码
        this.qrCodeBase64 = '';
      } finally {
        this.loadingQRCode = false;
      }
    },

    /**
     * 下载二维码
     */
    downloadQRCode() {
      if (!this.qrCodeBase64) {
        uni.showToast({
          title: '二维码未生成',
          icon: 'none'
        });
        return;
      }

      try {
        // #ifdef H5
        // H5环境下使用下载链接
        const link = document.createElement('a');
        link.href = this.qrCodeBase64;
        link.download = '天空影视二维码.png';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        uni.showToast({
          title: '二维码已保存',
          icon: 'success'
        });
        // #endif

        // #ifdef APP-PLUS
        // App环境下保存到相册
        uni.saveImageToPhotosAlbum({
          filePath: this.qrCodeBase64,
          success: () => {
            uni.showToast({
              title: '二维码已保存到相册',
              icon: 'success'
            });
          },
          fail: (error) => {
            logger.error('保存二维码失败:', error);
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
        // #endif

        // #ifdef MP
        // 小程序环境下保存到相册
        uni.saveImageToPhotosAlbum({
          filePath: this.qrCodeBase64,
          success: () => {
            uni.showToast({
              title: '二维码已保存到相册',
              icon: 'success'
            });
          },
          fail: (error) => {
            logger.error('保存二维码失败:', error);
            uni.showToast({
              title: '保存失败，请手动保存',
              icon: 'none'
            });
          }
        });
        // #endif

      } catch (error) {
        logger.error('下载二维码失败:', error);
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        });
      }
    },
    /**
     * 加载导航数据
     */
    async loadNavigations() {
      try {
        this.loading = true;
        this.error = null;

        // 并行加载导航数据和广告
        const [response, advertisements] = await Promise.all([
          getCachedNavigationList(),
          this.loadAdvertisements()
        ]);

        if (response && Array.isArray(response)) {
          // 保存原始导航数据
          this.originalNavigations = response;

          // 过滤移动端显示的导航项
          const mobileNavigations = filterMobileNavigations(response);

          // 转换为标签数据
          const apiTabs = convertToTabs(mobileNavigations);

          // 确保标签ID是唯一的字符串，并保留extraParams字段
          this.tabs = apiTabs.map(tab => {
            // 处理extraParams字段，确保它是原始字符串
            let extraParams = tab.extraParams !== undefined ? tab.extraParams : null;

            // 返回处理后的标签对象
            return {
              ...tab,
              id: String(tab.id),
              extraParams: extraParams
            };
          });
        } else {
          throw new Error('获取导航数据失败');
        }
      } catch (error) {
        this.error = error.message || '加载导航数据失败';

        // 初始化为空数组
        this.tabs = [];

        // 显示错误提示
        uni.showToast({
          title: '加载导航数据失败，请重试',
          icon: 'none',
          duration: 2000
        });
      } finally {
        this.loading = false;
      }
    },

    /**
     * 加载页面内广告（非开屏广告）
     */
    async loadAdvertisements() {
      try {
        logger.log('[页面广告] 开始加载页面内广告');

        // 获取广告列表
        const advertisements = await getAdvertisements();
        logger.log(`[页面广告] 获取到${advertisements.length}个广告`);

        // 过滤首页顶部广告
        const homeTopAds = filterAdvertisementsByPosition(advertisements, AdvertisementPosition.HomeTop);
        logger.log(`[页面广告] 过滤到${homeTopAds.length}个首页顶部广告`);

        // 所有顶部广告从上而下排列显示
        this.topBanners = homeTopAds;

        logger.log(`[页面广告] 设置顶部广告数量: ${this.topBanners.length}`);
        logger.log(`[页面广告] 顶部广告数据:`, this.topBanners);

        // 强制触发视图更新
        this.$forceUpdate();

        return advertisements;
      } catch (error) {
        logger.error('[页面广告] 加载广告失败:', error);
        this.topBanners = [];
        return [];
      }
    },

    /**
     * 导航到指定路径
     */
    navigateTo(path) {
      // 使用编程式导航跳转
      this.$router.push(path);
    },


    /**
     * 处理广告点击
     */
    handleBannerClick(banner) {
      if (banner && banner.redirectUrl) {
        if (banner.redirectUrl.startsWith('http')) {
          // 外部链接，在新窗口中打开
          window.open(banner.redirectUrl, '_blank');
        } else {
          // 内部页面，使用路由导航
          this.$router.push(banner.redirectUrl);
        }
      }
    },
    // 从URL或本地存储恢复标签状态
    restoreTabState() {
      let tabIndexParam = 0; // 默认为推荐标签（索引为0）

      // 尝试从URL获取状态 (H5环境)
      // #ifdef H5
      const urlTabIndex = this.getTabIndexFromUrl();
      if (urlTabIndex !== null) {
        tabIndexParam = urlTabIndex;
      }
      // #endif

      // 如果URL中没有标签参数，尝试从本地存储获取
      if (tabIndexParam === 0) {
        const storageTabIndex = this.getTabIndexFromStorage();
        if (storageTabIndex !== null) {
          tabIndexParam = storageTabIndex;
        }
      }

      // 验证并设置标签索引
      this.setValidTabIndex(tabIndexParam);
    },

    // 从URL获取标签索引
    getTabIndexFromUrl() {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');

        if (tabParam !== null) {
          // 如果是数字，直接返回索引
          if (!isNaN(parseInt(tabParam))) {
            return parseInt(tabParam);
          }

          // 如果是 UUID 格式（categotyId），查找对应的标签索引
          if (this.tabs && this.tabs.length > 0) {
            const index = this.tabs.findIndex(tab => tab.categotyId === tabParam);
            if (index !== -1) {
              return index;
            }
          }
        }
      } catch (e) {
        // URLSearchParams可能不被支持，使用简单的解析方法
        const search = window.location.search.substring(1);
        const params = search.split('&');
        for (let i = 0; i < params.length; i++) {
          const pair = params[i].split('=');
          if (pair[0] === 'tab') {
            const value = decodeURIComponent(pair[1]);

            // 如果是数字，直接返回索引
            if (!isNaN(parseInt(value))) {
              return parseInt(value);
            }

            // 如果是 UUID 格式（categotyId），查找对应的标签索引
            if (this.tabs && this.tabs.length > 0) {
              const index = this.tabs.findIndex(tab => tab.categotyId === value);
              if (index !== -1) {
                return index;
              }
            }

            break;
          }
        }
      }
      return null;
    },

    // 从本地存储获取标签索引
    getTabIndexFromStorage() {
      try {
        const savedData = uni.getStorageSync('currentTabIndex');

        // 如果是字符串，尝试解析JSON
        if (typeof savedData === 'string') {
          try {
            const parsedData = JSON.parse(savedData);

            // 新格式：包含 index 和 categotyId
            if (parsedData && typeof parsedData === 'object') {
              // 如果有 categotyId 并且标签数组已加载
              if (parsedData.categotyId && this.tabs && this.tabs.length > 0) {
                // 尝试通过 categotyId 查找标签索引
                const index = this.tabs.findIndex(tab => tab.categotyId === parsedData.categotyId);
                if (index !== -1) {
                  return index;
                }
              }

              // 如果没有找到匹配的 categotyId 或者没有 categotyId，使用索引
              if (parsedData.index !== undefined && !isNaN(parseInt(parsedData.index))) {
                return parseInt(parsedData.index);
              }
            }
          } catch (parseError) {
            // JSON 解析失败，可能是旧格式（纯数字）
            if (!isNaN(parseInt(savedData))) {
              return parseInt(savedData);
            }
          }
        }
        // 兼容旧格式：直接存储的数字
        else if (savedData !== null && !isNaN(parseInt(savedData))) {
          return parseInt(savedData);
        }
      } catch (e) {
        // 获取失败时默默返回null
        logger.error('从本地存储获取标签索引失败:', e);
      }
      return null;
    },

    // 设置有效的标签索引
    setValidTabIndex(index) {
      if (index >= 0 && index < this.tabs.length) {
        this.currentTabIndex = index;
        const currentTab = this.tabs[index];
        if (currentTab) {
          this.currentTab = currentTab.id || 'tab-' + index;
        }
      } else {
        this.currentTabIndex = 0; // 使用默认标签（推荐）
      }
    },

    // 加载开屏广告
    async loadSplashAds() {
      logger.log('[开屏广告] 开始加载广告数据');

      try {
        // 获取广告数据
        const advertisements = await getAdvertisements();

        // 过滤开屏广告
        const splashAds = filterAdvertisementsByPosition(advertisements, AdvertisementPosition.SplashScreen);
        logger.log(`[开屏广告] 获取到${splashAds.length}个开屏广告`);

        if (splashAds && splashAds.length > 0) {
          this.splashAds = splashAds;
          this.currentAdIndex = 0;
          this.currentSplashAd = this.splashAds[this.currentAdIndex];
          logger.log(`[开屏广告] 当前显示第1个广告，共${splashAds.length}个`);

          // 确保广告显示
          this.showSplashAd = true;
        } else {
          logger.log('[开屏广告] 没有找到开屏广告数据');
          this.showSplashAd = false;
        }
      } catch (error) {
        logger.error('[开屏广告] 加载广告数据失败:', error);
        this.showSplashAd = false;
      }
    },

    // 开始广告倒计时
    startAdCountdown() {
      logger.log(`[开屏广告] 开始广告倒计时，当前第${this.currentAdIndex + 1}个广告`);

      // 如果没有广告数据，不启动倒计时
      if (!this.splashAds || this.splashAds.length === 0) {
        logger.log('[开屏广告] 没有广告数据，不启动倒计时');
        this.showSplashAd = false;
        return;
      }

      // 确保当前广告数据存在
      if (!this.currentSplashAd) {
        this.currentAdIndex = 0;
        this.currentSplashAd = this.splashAds[this.currentAdIndex];
      }

      // 清除可能存在的定时器
      if (this.adTimer) {
        clearInterval(this.adTimer);
      }

      // 设置倒计时
      this.adCountdown = 10;
      this.adTimer = setInterval(() => {
        this.adCountdown--;

        // 只在倒计时为5秒和1秒时记录日志，减少日志数量
        if (this.adCountdown === 5 || this.adCountdown === 1) {
          logger.log(`[开屏广告] 广告倒计时: ${this.adCountdown}秒`);
        }

        if (this.adCountdown <= 0) {
          logger.log(`[开屏广告] 倒计时结束，当前第${this.currentAdIndex + 1}个广告，共${this.splashAds.length}个`);

          // 使用skipAd方法处理倒计时结束的逻辑
          this.skipAd();
        }
      }, 1000);
    },

    // 跳过广告
    skipAd() {
      logger.log(`[开屏广告] 跳过当前第${this.currentAdIndex + 1}个广告，共${this.splashAds.length}个`);

      // 清除定时器
      if (this.adTimer) {
        clearInterval(this.adTimer);
        this.adTimer = null;
      }

      // 如果还有下一个广告，切换到下一个
      if (this.currentAdIndex < this.splashAds.length - 1) {
        this.currentAdIndex++;
        this.currentSplashAd = this.splashAds[this.currentAdIndex];
        this.adCountdown = 10; // 重置倒计时
        logger.log(`[开屏广告] 切换到第${this.currentAdIndex + 1}个广告`);

        // 重新开始倒计时
        this.startAdCountdown();
      } else {
        // 所有广告已经显示完毕，关闭广告
        logger.log('[开屏广告] 所有广告已经显示完毕，关闭广告');
        this.showSplashAd = false;

        // 开屏广告关闭后，确保页面内广告可以正常显示
        logger.log('[开屏广告] 开屏广告关闭，页面内广告数量:', this.topBanners.length);

        // 如果页面内广告数据为空，尝试重新加载
        if (this.topBanners.length === 0) {
          logger.log('[开屏广告] 页面内广告为空，尝试重新加载');
          this.loadAdvertisements();
        }
      }
    },

    // 处理广告点击
    handleAdClick() {
      if (!this.currentSplashAd) return;

      console.log('点击广告:', this.currentSplashAd);
      logger.log('点击广告:', this.currentSplashAd);

      const redirectUrl = this.currentSplashAd.redirectUrl;
      if (redirectUrl) {
        if (redirectUrl.startsWith('http')) {
          // 外部链接，在新窗口中打开
          window.open(redirectUrl, '_blank');
        } else {
          // 内部页面
          uni.navigateTo({
            url: redirectUrl
          });
        }
      }

      // 点击广告后，使用skipAd方法处理后续逻辑
      this.skipAd();
    },
    /**
     * 检查标签ID是否有效
     * @param {string} tabId - 标签ID
     * @returns {boolean} 是否有效
     */
    isValidTabId(tabId) {
      // 如果标签数组为空，则只有recommend标签有效
      if (!this.tabs || this.tabs.length === 0) {
        return tabId === 'recommend';
      }

      return this.tabs.some(tab => tab.id === tabId);
    },
    // 确保当前选中的标签在可见区域内
    ensureTabVisible() {
      // 推荐标签（索引为0）通常在可见区域内，不需要滚动
      if (this.currentTabIndex === 0) return;

      setTimeout(() => {
        // #ifdef H5
        this.scrollTabIntoViewH5();
        // #endif

        // #ifndef H5
        this.scrollTabIntoViewApp();
        // #endif
      }, 100);
    },

    // H5环境下滚动标签到可见区域
    scrollTabIntoViewH5() {
      try {
        const el = document.getElementById(`tab-${this.currentTabIndex}`);
        if (el) {
          el.scrollIntoView({behavior: 'smooth', block: 'center', inline: 'center'});
        }
      } catch (e) {
        // 静默处理滚动失败
      }
    },

    // App环境下滚动标签到可见区域
    scrollTabIntoViewApp() {
      const query = uni.createSelectorQuery();
      query.select(`#tab-${this.currentTabIndex}`).boundingClientRect(data => {
        if (data) {
          // 这里可以根据标签位置进行自定义滚动逻辑
          // 由于没有直接的scrollIntoView等效方法，可能需要计算滚动位置
          // 此处留空，根据实际需求实现
        }
      }).exec();
    },
    // 切换标签页
    switchTab(index) {
      logger.log(`切换标签，从 ${this.currentTabIndex} 到 ${index}`);

      // 避免重复切换
      if (this.currentTabIndex === index) {
        logger.log('已经在当前标签，不需要切换');
        return;
      }

      // 检查索引是否有效
      if (index < 0 || index >= this.tabs.length) {
        logger.log('标签索引无效');
        return;
      }

      // 获取当前标签
      const currentTab = this.tabs[index];
      logger.log('切换到标签:', currentTab.name);

      // 更新当前标签索引
      this.currentTabIndex = index;

      // 更新当前标签ID（为了兼容性）
      this.currentTab = currentTab.id || 'tab-' + index;

      // 保存当前标签到本地存储
      try {
        // 保存索引和categotyId（如果有）
        const storageData = {
          index: index,
          categotyId: currentTab.categotyId || null
        };
        uni.setStorageSync('currentTabIndex', JSON.stringify(storageData));
      } catch (e) {
        logger.error('保存标签状态失败:', e);
      }

      // 更新URL参数 (仅H5环境)
      // #ifdef H5
      let urlParam;
      if (index === 0) {
        // 推荐标签使用index=0
        urlParam = '0';
      } else if (currentTab.categotyId) {
        // 其他标签使用categotyId
        urlParam = currentTab.categotyId;
      } else {
        // 如果没有categotyId，回退到使用索引
        urlParam = index;
      }

      const url = `?tab=${urlParam}`;
      if (window.history && window.history.pushState) {
        window.history.pushState({tab: urlParam}, '', url);
      }
      // #endif

      // 确保新选中的标签可见
      this.$nextTick(() => {
        this.ensureTabVisible();
      });
    },


    /**
     * 处理切换标签事件
     * @param {number|string} indexOrId - 标签索引或categotyId
     */
    handleSwitchTab(indexOrId) {
      // 如果是数字，直接使用索引
      if (typeof indexOrId === 'number') {
        // 检查索引是否有效
        if (indexOrId >= 0 && indexOrId < this.tabs.length) {
          this.switchTab(indexOrId);
        }
        return;
      }

      // 如果是字符串，可能是categotyId
      if (typeof indexOrId === 'string') {
        // 尝试解析为数字
        const parsedIndex = parseInt(indexOrId);
        if (!isNaN(parsedIndex) && parsedIndex >= 0 && parsedIndex < this.tabs.length) {
          this.switchTab(parsedIndex);
          return;
        }

        // 如果不是数字，可能是categotyId
        const index = this.tabs.findIndex(tab => tab.categotyId === indexOrId);
        if (index !== -1) {
          this.switchTab(index);
          return;
        }
      }

      // 如果无法匹配，切换到默认标签
      logger.warn('无法匹配标签，切换到默认标签');
      this.switchToDefaultTab();
    },

    /**
     * 根据类别名称切换标签页
     * @param {string} category - 类别名称
     */
    handleSwitchTabByCategory(category) {
      // 记录日志
      logger.log('收到类别切换请求:', category);

      // 在标签数组中查找匹配的标签
      const foundIndex = this.findTabIndexByCategory(category);
      logger.log('查找结果索引:', foundIndex);

      // 如果找到了匹配的标签，切换到该标签
      if (foundIndex !== -1) {
        this.switchTab(foundIndex);
      } else {
        this.switchToDefaultTab();
      }
    },

    /**
     * 根据类别名称查找标签索引
     * @param {string} category - 类别名称
     * @returns {number} 标签索引，如果未找到返回-1
     */
    findTabIndexByCategory(category) {
      if (!category) return -1;

      // 将类别名称转换为小写以进行不区分大小写的比较
      const lowerCategory = category.toLowerCase();

      // 1. 先尝试精确匹配 categotyName
      let index = this.tabs.findIndex(tab =>
        tab.categotyName && tab.categotyName.toLowerCase() === lowerCategory
      );
      if (index !== -1) {
        logger.log('通过categotyName精确匹配到标签:', this.tabs[index].name);
        return index;
      }

      // 2. 尝试精确匹配 name
      index = this.tabs.findIndex(tab =>
        tab.name && tab.name.toLowerCase() === lowerCategory
      );
      if (index !== -1) {
        logger.log('通过name精确匹配到标签:', this.tabs[index].name);
        return index;
      }

      // 3. 尝试部分匹配 categotyName
      index = this.tabs.findIndex(tab =>
        tab.categotyName && tab.categotyName.toLowerCase().includes(lowerCategory)
      );
      if (index !== -1) {
        logger.info('通过categotyName部分匹配到标签:', this.tabs[index]);
        return index;
      }

      // 4. 尝试部分匹配 name
      index = this.tabs.findIndex(tab =>
        tab.name && tab.name.toLowerCase().includes(lowerCategory)
      );
      if (index !== -1) {
        logger.info('通过name部分匹配到标签:', this.tabs[index]);
        return index;
      }

      // 5. 尝试检查类别是否包含标签名称
      for (let i = 0; i < this.tabs.length; i++) {
        const tab = this.tabs[i];
        if ((tab.categotyName && lowerCategory.includes(tab.categotyName.toLowerCase())) ||
            (tab.name && lowerCategory.includes(tab.name.toLowerCase()))) {
          logger.info('通过反向匹配到标签:', tab);
          return i;
        }
      }

      logger.info('没有找到匹配的标签');
      return -1;
    },

    /**
     * 切换到默认标签（推荐）
     */
    switchToDefaultTab() {
      logger.log('切换到默认标签(推荐)');
      // 直接切换到推荐标签（索引0）
      this.switchTab(0);
    },

    /**
     * 处理获取标签数据事件
     * @param {Function} callback - 回调函数
     */
    handleGetTabsData(callback) {
      if (typeof callback === 'function') {
        callback(this.tabs);
      }
    },

    /**
     * 跳转到推荐页面
     */
    goToRecommend() {
      // 直接切换到第一个标签（推荐标签）
      this.switchTab(0);
    },


  }
};
</script>

<style scoped>
/* Logo 区域样式 */
.logo-area {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}

.app-logo {
  width: 60rpx;
  height: 60rpx;
}

/* Logo 链接样式 */
.logo-link {
  cursor: pointer;
  transition: opacity 0.3s;
  display: flex;
  align-items: center;
  padding: 10rpx;
}

.logo-link:active {
  opacity: 0.7;
}

/* 中间标签区域 */
.tabs-container {
  flex: 1;
  height: 100%;
  margin: 0 10rpx;
  white-space: nowrap;
  min-width: 50%;
  max-width: calc(100% - 240rpx);
}

/* 隐藏滚动条 */
.tabs-container::-webkit-scrollbar {
  display: none;
}

.tab-item {
  display: inline-block;
  position: relative;
  padding: 0 30rpx;
  text-align: center;
  font-size: 37rpx;
  color: #c8c8cc;
  height: 100%;
  line-height: 100rpx;
  transition: all 0.3s ease;
}

.active-tab {
  color: #ffffff;
  font-weight: bold;
  font-size: 40rpx;
}

/* 为所有tab-item添加过渡位置样式，确保标记的正确定位和动画 */
.tab-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  transform-origin: center;
  width: 40rpx;
  height: 6rpx;
  background-color: transparent;
  border-radius: 3rpx;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), background-color 0.3s ease;
}

.active-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(1);
  transform-origin: center;
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(315deg,#fe748c,#fe748e);
  border-radius: 3rpx;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), background-color 0.3s ease;
}

.options_box {
  position: absolute;
  width: 100%;

  .overlay {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100vh;
    z-index: 9999;
    background-color: rgba(0, 0, 0, .7);
  }

  .options_warp {
    z-index: 999999;
    position: absolute;
    right: 10vw;
    top: 30rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    image {
      width: 20rpx;
      display: block;
    }
  }

  .options_con {
    background: rgba(37, 37, 44, .9);
    padding:40rpx 30rpx;
    align-items: start;

    ul {
      display: flex;
      flex-direction: column;
      gap: 40rpx;

      li{
        display: flex;
        align-items: center;
        font-size: 28rpx;
        flex-direction: row;

        .lishi:before, .pindao:before, .save:before {
          width: 36rpx;
          height: 36rpx;
          margin: 0 10rpx 0 0;
        }
      }
    }

  }
}

/* 顶部通栏广告样式 */
.top-banners {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.top-banner {
  width: 100%;
  cursor: pointer;
}

.banner-image {
  width: 100%;
  display: block;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 116, 61, 0.2);
  border-top-color: #ff743d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #969699;
}

/* 错误提示样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.error-text {
  font-size: 28rpx;
  color: #969699;
  text-align: center;
  margin-bottom: 20rpx;
}

.error-retry {
  padding: 10rpx 30rpx;
  background: rgba(255, 116, 61, 0.1);
  border: 1px solid #ff743d;
  border-radius: 30rpx;
  color: #ff743d;
  font-size: 24rpx;
}

/* 开屏广告样式 */
.splash-ad {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 9999999;
  background-color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  /* 确保完全覆盖其他内容 */
  pointer-events: auto;
}

.splash-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.skip-container {
  position: absolute;
  top: 40px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 20px;
  padding: 5px 10px;
  z-index: 10000000;
}

.skip-text {
  color: #fff;
  font-size: 14px;
}

.ad-label {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  z-index: 10000000;
}



.savepage{
  z-index: 999999;
  left: 50%;
  top: 50%;
  width: 70%;
  padding: 30rpx;
  border-radius: 10rpx;
  transform: translate(-50%, -50%);
  position: fixed;
  color: #2a2a2a;
  background: linear-gradient(#fe748e,#fef3ef,70%,#fef3ef);

  .close{
    position: absolute;
    right: -15rpx;
    top: -15rpx;
    width: 50rpx;
    height: 50rpx;
    border-radius: 25rpx;
    background: #fe748e;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24rpx;

    svg{
      width: 30rpx;
      height: 30rpx;
    }

  }

  h3{
    font-size: 40rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
    color: #fff;
  }


  .savecon{
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;

    ul{
      line-height: 50rpx;
    }

    view:nth-child(2){
      display: flex;
      flex-direction: column;
      text-align: center;

      img{
        width: 200rpx;
        padding: 10rpx;
        border-radius: 10rpx;
        background: #fff;
      }
    }
  }

  .jieping{
    margin-top: 40rpx;
    padding: 8rpx 30rpx;
    color: #fe748e;
    font-weight: 700;
    text-align: center;
    border: 4rpx solid #fe748e;
    border-radius: 8rpx;
  }

  .qr-loading {
    width: 200rpx;
    height: 200rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border-radius: 10rpx;
    font-size: 24rpx;
    color: #666;
    margin-bottom: 10rpx;
  }
}
.right-icons .icon-item a.navigator-wrap{
  display: flex;
}

</style>


<template>
  <view class="top-tabs">
    <!-- Logo 部分 -->
    <view class="logo-area">
      <view @click="goToRecommend" class="logo-link">
        <image class="app-logo" src="@/static/logo.png" mode="aspectFit"/>
      </view>
    </view>

    <view class="right-icons">

      <ul class="right-menu">
        <li @click="navigateTo('/pages/search/search')">
          <i class="icon sousuo"></i>搜索
        </li>
        <li @click="navigateTo('/pages/history/history')">
          历史
        </li>
        <li @click="navigateTo('/pages/navigation/navigation')">
          全部频道
        </li>
        <li @click="savePage">
          保存网址
        </li>
      </ul>
    </view>
  </view>
  <view class="savepage" v-show="showSavePage">
    <view class="close" @click="hideSavePage"><svg t="1748242082575" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2618"><path d="M576 512l277.333333 277.333333-64 64-277.333333-277.333333L234.666667 853.333333 170.666667 789.333333l277.333333-277.333333L170.666667 234.666667 234.666667 170.666667l277.333333 277.333333L789.333333 170.666667 853.333333 234.666667 576 512z" fill="#ffffff"></path></svg></view>
    <h3>最新公告</h3>
    <view class="savecon">
      <view>
        <ul>
          <li>站最新域名:</li>
          <li>{{ systemSetting.domain }}</li>
          <li>发送任意邮件至邮箱</li>
          <li>{{ systemSetting.contactEmail }}</li>
          <li>秒得最新地址</li>
        </ul>
      </view>
      <view>
        <view v-if="loadingQRCode" class="qr-loading">
          正在生成二维码...
        </view>
        <img v-else-if="qrCodeBase64" :src="qrCodeBase64" alt="" download="天空影视二维码.png" @click="downloadQRCode">
        <img v-else src="data:image/png;base64,+JJggg==" alt="" download="天空影视二维码.png">
        点击保存二维码
      </view>
    </view>
    <view class="jieping">
      请截屏保存，以防走失!
    </view>
  </view>

</template>

<script>
import { getCachedSystemSetting } from '@/api/systemSetting'
import { generateQRCodeForUniApp, formatUrl } from '@/utils/qrcode'
import logger from "@/utils/logger";

export default {
  name: "head.vue",
  data(){
    return {
      showSavePage: false,
      systemSetting: {
        domain: 'aaa.com',
        contactEmail: '<EMAIL>'
      },
      qrCodeBase64: '', // 二维码base64数据
      loadingQRCode: false // 二维码生成状态
    }
    },
  async mounted() {
    // 组件加载时加载系统设置
    await this.loadSystemSetting();
  },
  methods:{

    navigateTo(path) {
      // 使用编程式导航跳转
      this.$router.push(path);
    },

    // 切换选项菜单显示状态
    toggleOptionsBox() {
      this.showOptionsBox = !this.showOptionsBox;
    },
    // 隐藏选项菜单
    hideOptionsBox() {
      this.showOptionsBox = false;
      this.showSavePage = false
    },

    savePage() {
      this.showSavePage = true; // 显示弹出框
    },
    hideSavePage() {
      this.showSavePage = false; // 隐藏弹出框
      this.showOptionsBox = false;
    },

    /**
     * 加载系统设置
     */
    async loadSystemSetting() {
      try {
        logger.log('[系统设置] 开始加载系统设置');

        // 获取系统设置数据
        const setting = await getCachedSystemSetting();

        if (setting) {
          // 更新系统设置数据
          this.systemSetting = {
            domain: setting.domain || 'aaa.com',
            contactEmail: setting.contactEmail || '<EMAIL>'
          };

          logger.log('[系统设置] 系统设置加载成功:', this.systemSetting);

          // 生成二维码
          await this.generateQRCode();
        } else {
          logger.warn('[系统设置] 未获取到系统设置数据，使用默认值');
        }
      } catch (error) {
        logger.error('[系统设置] 加载系统设置失败:', error);
        // 使用默认值
        this.systemSetting = {
          domain: 'aaa.com',
          contactEmail: '<EMAIL>'
        };
      }
    },

    /**
     * 下载二维码
     */
    downloadQRCode() {
      if (!this.qrCodeBase64) {
        uni.showToast({
          title: '二维码未生成',
          icon: 'none'
        });
        return;
      }

      try {
        // #ifdef H5
        // H5环境下使用下载链接
        const link = document.createElement('a');
        link.href = this.qrCodeBase64;
        link.download = '天空影视二维码.png';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        uni.showToast({
          title: '二维码已保存',
          icon: 'success'
        });
        // #endif

        // #ifdef APP-PLUS
        // App环境下保存到相册
        uni.saveImageToPhotosAlbum({
          filePath: this.qrCodeBase64,
          success: () => {
            uni.showToast({
              title: '二维码已保存到相册',
              icon: 'success'
            });
          },
          fail: (error) => {
            logger.error('保存二维码失败:', error);
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
        // #endif

        // #ifdef MP
        // 小程序环境下保存到相册
        uni.saveImageToPhotosAlbum({
          filePath: this.qrCodeBase64,
          success: () => {
            uni.showToast({
              title: '二维码已保存到相册',
              icon: 'success'
            });
          },
          fail: (error) => {
            logger.error('保存二维码失败:', error);
            uni.showToast({
              title: '保存失败，请手动保存',
              icon: 'none'
            });
          }
        });
        // #endif

      } catch (error) {
        logger.error('下载二维码失败:', error);
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        });
      }
    },

    async generateQRCode() {
      try {
        this.loadingQRCode = true;

        const originalUrl = this.systemSetting.domain;
        const formattedUrl = formatUrl(originalUrl);

        logger.log('[二维码] 原始网址:', originalUrl);
        logger.log('[二维码] 格式化后:', formattedUrl);

        // 生成网址的二维码（使用格式化后的URL）
        const qrCodeData = await generateQRCodeForUniApp(originalUrl, {
          size: 200,
          backgroundColor: '#FFFFFF',
          foregroundColor: '#000000',
          autoFormat: true // 启用自动格式URL
        });

        this.qrCodeBase64 = qrCodeData;
        logger.log('[二维码] 二维码生成成功');

        // 显示成功提示
        if (originalUrl !== formattedUrl) {
          logger.log(`[二维码] URL已自动格式化: ${originalUrl} -> ${formattedUrl}`);
        }

      } catch (error) {
        logger.error('[二维码] 生成二维码失败:', error);
        // 使用默认的空白二维码
        this.qrCodeBase64 = '';
      } finally {
        this.loadingQRCode = false;
      }
    },


    goToRecommend() {
      // 直接切换到第一个标签（推荐标签）
      this.switchTab(0);
    },
  }
}


</script>


<style scoped>
/* Logo 区域样式 */
.logo-area {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}

.app-logo {
  width: 60rpx;
  height: 60rpx;
}

/* Logo 链接样式 */
.logo-link {
  cursor: pointer;
  transition: opacity 0.3s;
  display: flex;
  align-items: center;
  padding: 10rpx;
}

.logo-link:active {
  opacity: 0.7;
}
.right-menu{
  color: #fff;
}
/* 中间标签区域 */
.tabs-container {
  flex: 1;
  height: 100%;
  margin: 0 10rpx;
  white-space: nowrap;
  min-width: 50%;
  max-width: calc(100% - 240rpx);
}

/* 隐藏滚动条 */
.tabs-container::-webkit-scrollbar {
  display: none;
}

.tab-item {
  display: inline-block;
  position: relative;
  padding: 0 30rpx;
  text-align: center;
  font-size: 37rpx;
  color: #c8c8cc;
  height: 100%;
  line-height: 100rpx;
  transition: all 0.3s ease;
}

.active-tab {
  color: #ffffff;
  font-weight: bold;
  font-size: 40rpx;
}

/* 为所有tab-item添加过渡位置样式，确保标记的正确定位和动画 */
.tab-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  transform-origin: center;
  width: 40rpx;
  height: 6rpx;
  background-color: transparent;
  border-radius: 3rpx;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), background-color 0.3s ease;
}

.active-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(1);
  transform-origin: center;
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(315deg,#fe748c,#fe748e);
  border-radius: 3rpx;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), background-color 0.3s ease;
}

.options_box {
  position: absolute;
  width: 100%;

  .overlay {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100vh;
    z-index: 9999;
    background-color: rgba(0, 0, 0, .7);
  }

  .options_warp {
    z-index: 999999;
    position: absolute;
    right: 10vw;
    top: 30rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    image {
      width: 20rpx;
      display: block;
    }
  }

  .options_con {
    background: rgba(37, 37, 44, .9);
    padding:40rpx 30rpx;
    align-items: start;

    ul {
      display: flex;
      flex-direction: column;
      gap: 40rpx;

      li{
        display: flex;
        align-items: center;
        font-size: 28rpx;
        flex-direction: row;

        .lishi:before, .pindao:before, .save:before {
          width: 36rpx;
          height: 36rpx;
          margin: 0 10rpx 0 0;
        }
      }
    }

  }
}

.savepage{
  z-index: 999999;
  left: 50%;
  top: 50%;
  width: 70%;
  padding: 30rpx;
  border-radius: 10rpx;
  transform: translate(-50%, -50%);
  position: fixed;
  color: #2a2a2a;
  background: linear-gradient(#fe748e,#fef3ef,70%,#fef3ef);

  .close{
    position: absolute;
    right: -15rpx;
    top: -15rpx;
    width: 50rpx;
    height: 50rpx;
    border-radius: 25rpx;
    background: #fe748e;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24rpx;

    svg{
      width: 30rpx;
      height: 30rpx;
    }

  }

  h3{
    font-size: 40rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
    color: #fff;
  }


  .savecon{
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;

    ul{
      line-height: 50rpx;
    }

    view:nth-child(2){
      display: flex;
      flex-direction: column;
      text-align: center;

      img{
        width: 200rpx;
        padding: 10rpx;
        border-radius: 10rpx;
        background: #fff;
      }
    }
  }

  .jieping{
    margin-top: 40rpx;
    padding: 8rpx 30rpx;
    color: #fe748e;
    font-weight: 700;
    text-align: center;
    border: 4rpx solid #fe748e;
    border-radius: 8rpx;
  }

  .qr-loading {
    width: 200rpx;
    height: 200rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border-radius: 10rpx;
    font-size: 24rpx;
    color: #666;
    margin-bottom: 10rpx;
  }
}
.right-icons .icon-item a.navigator-wrap{
  display: flex;
}
</style>
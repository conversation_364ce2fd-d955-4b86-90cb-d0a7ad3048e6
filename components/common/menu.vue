<template>
  <ul class="menu">
    <!-- 加载中提示 -->
    <li v-if="loading">
      <view>正在加载...</view>
    </li>

    <!-- 错误提示 -->
    <li v-else-if="error">
      <view>加载失败</view>
      <view>
        <span @click="loadMenuData">点击重试</span>
      </view>
    </li>

    <!-- 菜单内容 -->
    <template v-else>
      <li v-for="(category, index) in menuCategories" :key="index">
        <view class="category">{{ category.name }}</view>
        <scroll-view
          class="tags"
          scroll-x="true"
          :show-scrollbar="false"
          :enable-flex="true"
        >
          <!-- 子分类加载中 -->
          <span v-if="category.loading">正在加载...</span>
          <!-- 子分类列表 -->
          <template v-else>
            <span
              v-for="(subCategory, subIndex) in category.subCategories"
              :key="subIndex"
              @click="navigateToCategory(subCategory)"
            >
              {{ subCategory.name }}
            </span>
            <!-- 无子分类时显示默认内容 -->
            <template v-if="!category.subCategories || category.subCategories.length === 0">
              <span>暂无内容</span>
            </template>
          </template>
        </scroll-view>
      </li>
    </template>
  </ul>
</template>

<script>
import { getCachedNavigationList } from '@/api/navigationCache';
import { getVideoTagList, TagDisplayType } from '@/api/videoTag';

export default {
  name: "menu",
  data() {
    return {
      loading: false,
      error: null,
      menuCategories: [],        // 菜单分类数据
      allTagsCache: null,        // 缓存所有标签数据
    };
  },

  // 组件挂载时加载数据
  mounted() {
    this.loadMenuData();
  },

  methods: {
    // 加载菜单数据
    async loadMenuData() {
      this.loading = true;
      this.error = null;

      try {
        // 获取导航数据
        const navigations = await getCachedNavigationList();

        if (navigations && Array.isArray(navigations)) {
          // 处理导航数据，提取主分类
          await this.processMenuData(navigations);
        } else {
          throw new Error('获取导航数据失败');
        }
      } catch (error) {
        console.error('加载菜单数据失败:', error);
        this.error = error.message || '加载菜单数据失败';
      } finally {
        this.loading = false;
      }
    },

    // 处理菜单数据
    async processMenuData(navigations) {
      // 过滤移动端显示的导航项
      const mobileNavigations = navigations.filter(nav => nav.isH5Display);

      // 按categotyName分组
      const groupedNavigations = {};
      mobileNavigations.forEach(nav => {
        const mainCategory = nav.name;
        if (mainCategory) {
          if (!groupedNavigations[mainCategory]) {
            groupedNavigations[mainCategory] = [];
          }
          groupedNavigations[mainCategory].push(nav);
        }
      });

      // 转换为菜单格式
      this.menuCategories = Object.keys(groupedNavigations).map((categoryName, index) => {
        const categoryItems = groupedNavigations[categoryName];
        const categoryId = categoryItems.length > 0 ? categoryItems[0].categotyId : null;

        return {
          name: categoryName,
          categoryId: categoryId,
          loading: true,          // 初始加载状态
          subCategories: [],      // 子分类数据
        };
      });

      // 一次性获取所有标签数据（优化：一次性获取）
      try {
        this.allTagsCache = await getVideoTagList([TagDisplayType.Category], null);
        console.log('一次性加载的标签数据:', this.allTagsCache);

        // 为所有分类加载子分类数据
        await this.loadAllSubCategories(groupedNavigations);
      } catch (error) {
        console.warn('加载标签数据失败:', error);
        this.allTagsCache = null;
        // 如果一次性加载失败，回退到逐个加载方式
        await this.loadAllSubCategoriesIndividually(groupedNavigations);
      }
    },

    // 一次性加载所有子分类数据（使用缓存的标签数据）
    async loadAllSubCategories(groupedNavigations) {
      if (!this.allTagsCache || !Array.isArray(this.allTagsCache)) {
        return;
      }

      // 创建分类ID到分类名称的映射
      const categoryIdToName = {};

      for (let i = 0; i < this.menuCategories.length; i++) {
        const category = this.menuCategories[i];
        if (category.categoryId) {
          categoryIdToName[category.categoryId] = category.name;
        }
      }

      // 为每个分类分组标签数据
      for (let i = 0; i < this.menuCategories.length; i++) {
        const category = this.menuCategories[i];

        if (category.categoryId) {
          // 从所有标签中筛选属于当前分类的标签
          const tags = this.allTagsCache.filter(tag => {
            // 根据标签的types字段或者其他方式判断是否属于当前分类
            if (tag.types && Array.isArray(tag.types)) {
              return tag.types.includes(category.name) || tag.types.includes(category.categoryId);
            }
            // 如果没有types字段，暂时将所有标签分配给所有分类（fallback方案）
            return true;
          });

          // 转换为子分类格式
          category.subCategories = tags.map(tag => ({
            id: tag.id,
            name: tag.tagName,
            categoryId: category.categoryId,
            categoryName: category.name
          }));
        }

        // 设置加载完成
        category.loading = false;
      }
    },

    // 回退方案：逐个加载所有子分类数据
    async loadAllSubCategoriesIndividually(groupedNavigations) {
      for (let i = 0; i < this.menuCategories.length; i++) {
        const category = this.menuCategories[i];

        if (category.categoryId) {
          try {
            const tags = await getVideoTagList([TagDisplayType.Category], category.categoryId);

            if (tags && Array.isArray(tags)) {
              category.subCategories = tags.map(tag => ({
                id: tag.id,
                name: tag.tagName,
                categoryId: category.categoryId,
                categoryName: category.name
              }));
            }
          } catch (error) {
            console.error(`加载${category.name}的子分类失败:`, error);
            category.subCategories = [];
          }
        }

        // 设置加载完成
        category.loading = false;
      }
    },



    // 导航到分类页面
    navigateToCategory(subCategory) {
      if (!subCategory) return;

      // 构建URL参数
      const params = {
        videoType: subCategory.categoryName,
        videoTypeId: subCategory.categoryId,
        videoTag: subCategory.name,
        title: subCategory.name,
        sorting: 'VideoAddTime desc'
      };

      // 构建URL查询字符串
      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');

      // 导航到列表页
      uni.navigateTo({
        url: `/pages/list/list?${queryString}`
      });

      // 触发自定义事件，通知父组件
      this.$emit('category-selected', subCategory);
    }
  }
};
</script>

<style scoped>
.menu{
  margin-bottom: 20rpx;
}

.menu li {
  display: flex;
  padding: 10rpx;
  background: #111111;
}
.menu li:nth-child(2n) {
  background: #2e2e2e;
}

.category{
  width: 80rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-right: 20rpx;
  color: #fe748e;
  text-align: center;
}
.category:after{
  content: '';
  display: block;
  height: 50%;
  width: 1px;
  background-color: #5b5b5b;
}
.tags {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
  overflow-x: auto;
  width: calc(100% - 120rpx);
}

.tags span {
  flex-shrink: 0;
  white-space: nowrap;
  margin-right: 16rpx;
  background: #2e2e2e;
  border: 1px #5b5b5b solid;
  padding: 0 14rpx;
  border-radius: 6rpx;
  width: 100rpx;
  overflow: hidden;
  display: inline-block;
  text-align: center;
  line-height: 48rpx;
  font-size: 24rpx;
  color: #e3e3e5;


}

</style>